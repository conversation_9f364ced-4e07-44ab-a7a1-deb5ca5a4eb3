{"Version": 1, "WorkspaceRootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\alsi.tab.kit.core\\models\\cintemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\models\\cintemplate.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2306ABE4-156F-48BF-A890-C67EF1C28C67}|..\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.common.parsers\\dbc\\models\\envdatatype.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.core\\services\\capl\\caplparamtypes.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\capl\\caplparamtypes.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.core\\services\\sourcefileparamservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\sourcefileparamservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2306ABE4-156F-48BF-A890-C67EF1C28C67}|..\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.common.parsers\\cin\\cinparserutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2306ABE4-156F-48BF-A890-C67EF1C28C67}|..\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.common.parsers\\cin\\cinparser.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{32F4AFD7-A5C8-4D09-A996-C849891054DC}|Alsi.Tab.Kit\\Alsi.Tab.Kit.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit\\cin\\templates\\can\\can_config.cin||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{32F4AFD7-A5C8-4D09-A996-C849891054DC}|Alsi.Tab.Kit\\Alsi.Tab.Kit.csproj|solutionrelative:alsi.tab.kit\\cin\\templates\\can\\can_config.cin||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{D45049C2-92F3-A823-AD5F-89EE4256D61B}|..\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.common.utils\\alsi.common.utils.csproj||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{9E8C78A1-6D4A-4D48-9D98-9829354E0C44}|Alsi.Tab.Kit.UnitTests\\Alsi.Tab.Kit.UnitTests.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.unittests\\cinparser_parsefiletests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9E8C78A1-6D4A-4D48-9D98-9829354E0C44}|Alsi.Tab.Kit.UnitTests\\Alsi.Tab.Kit.UnitTests.csproj|solutionrelative:alsi.tab.kit.unittests\\cinparser_parsefiletests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9E8C78A1-6D4A-4D48-9D98-9829354E0C44}|Alsi.Tab.Kit.UnitTests\\Alsi.Tab.Kit.UnitTests.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.unittests\\caplresources\\globalelements.cin||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{9E8C78A1-6D4A-4D48-9D98-9829354E0C44}|Alsi.Tab.Kit.UnitTests\\Alsi.Tab.Kit.UnitTests.csproj|solutionrelative:alsi.tab.kit.unittests\\caplresources\\globalelements.cin||{8B382828-6202-11D1-8870-0000F87579D2}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 6, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "EnvDataType.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Dbc\\Models\\EnvDataType.cs", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.Common.Parsers\\Dbc\\Models\\EnvDataType.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Dbc\\Models\\EnvDataType.cs", "RelativeToolTip": "..\\Alsi.Common\\Alsi.Common.Parsers\\Dbc\\Models\\EnvDataType.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAQAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:11:33.091Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CaplParamTypes.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamTypes.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamTypes.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamTypes.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamTypes.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:11:17.186Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "SourceFileParamService.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "ViewState": "AgIAANQAAAAAAAAAAAApwOYAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:10:39.567Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "CinParserUtils.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParserUtils.cs", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParserUtils.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParserUtils.cs", "RelativeToolTip": "..\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParserUtils.cs", "ViewState": "AgIAAPIBAAAAAAAAAAAQwAcCAAA6AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:05:36.21Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "can_config.cin", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit\\cin\\templates\\can\\can_config.cin", "RelativeDocumentMoniker": "Alsi.Tab.Kit\\cin\\templates\\can\\can_config.cin", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit\\cin\\templates\\can\\can_config.cin", "RelativeToolTip": "Alsi.Tab.Kit\\cin\\templates\\can\\can_config.cin", "ViewState": "AgIAAEkAAAAAAAAAAAAAAFoAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-10T07:05:17.249Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "CinTemplate.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Models\\CinTemplate.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Models\\CinTemplate.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Models\\CinTemplate.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Models\\CinTemplate.cs", "ViewState": "AgIAAAwAAAAAAAAAAAAowB8AAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:05:36.281Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "CinParser.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParser.cs", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParser.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParser.cs", "RelativeToolTip": "..\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParser.cs", "ViewState": "AgIAABkAAAAAAAAAAAAywC4AAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T07:04:22.054Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "Alsi.Common.Utils", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj", "RelativeToolTip": "..\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000758|", "WhenOpened": "2025-07-10T07:04:17.383Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "CinParser_ParseFileTests.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.UnitTests\\CinParser_ParseFileTests.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.UnitTests\\CinParser_ParseFileTests.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.UnitTests\\CinParser_ParseFileTests.cs", "RelativeToolTip": "Alsi.Tab.Kit.UnitTests\\CinParser_ParseFileTests.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T06:23:35.541Z", "EditorCaption": ""}]}]}, {"Orientation": 0, "VerticalTabListWidth": 256, "FloatingWindowState": {"Id": "bc4925fc-25a0-4f8d-856b-2f3d2181ba3c", "Display": 0, "X": 432, "Y": 157, "Width": 906, "Height": 666, "WindowState": 2}, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 9, "Title": "GlobalElements.cin", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.UnitTests\\CaplResources\\GlobalElements.cin", "RelativeDocumentMoniker": "Alsi.Tab.Kit.UnitTests\\CaplResources\\GlobalElements.cin", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.UnitTests\\CaplResources\\GlobalElements.cin", "RelativeToolTip": "Alsi.Tab.Kit.UnitTests\\CaplResources\\GlobalElements.cin", "ViewState": "AgIAAGkAAAAAAAAAAAAAAHQAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-10T06:58:42.663Z", "EditorCaption": ""}]}]}]}