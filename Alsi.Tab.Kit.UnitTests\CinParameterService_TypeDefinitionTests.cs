using Alsi.Common.Parsers.Cin;
using Alsi.Tab.Kit.Core.Services;

namespace Alsi.Tab.Kit.UnitTests;

public class CinParameterService_TypeDefinitionTests
{
    private readonly CinParameterService _cinParameterService;

    public CinParameterService_TypeDefinitionTests()
    {
        CinParser.Initialize();
        _cinParameterService = new CinParameterService();
    }

    [Fact]
    public void ParseCinFile_ShouldReturnTypeDefinitions_WhenFileContainsStructsAndEnums()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";

        // Act
        var result = _cinParameterService.ParseCinFile("file", null, caplPath);

        // Assert
        result.ShouldNotBeNull();
        result.TypeDefinitions.ShouldNotBeNull();
        result.TypeDefinitions.Count.ShouldBeGreaterThan(0);

        // 验证包含结构体定义
        var structDefinitions = result.TypeDefinitions.Where(td => td.Definition.Contains("struct")).ToList();
        structDefinitions.Count.ShouldBeGreaterThan(0);

        // 验证包含枚举定义
        var enumDefinitions = result.TypeDefinitions.Where(td => td.Definition.Contains("enum")).ToList();
        enumDefinitions.Count.ShouldBeGreaterThan(0);

        // 验证特定的枚举定义
        var vehicleCarModeEnum = result.TypeDefinitions.FirstOrDefault(td => td.TypeName == "VEHICLE_CARMODE");
        vehicleCarModeEnum.ShouldNotBeNull();
        vehicleCarModeEnum.Definition.ShouldContain("CARMODE_NORMAL=0");
        vehicleCarModeEnum.Definition.ShouldContain("CARMODE_TRANSPORT");
    }

    [Fact]
    public void ParseCinFile_ShouldReturnEmptyTypeDefinitions_WhenFileContainsNoStructsOrEnums()
    {
        // Arrange
        var caplPath = "./CaplResources/ParamDemo1.cin";

        // Act
        var result = _cinParameterService.ParseCinFile("file", null, caplPath);

        // Assert
        result.ShouldNotBeNull();
        result.TypeDefinitions.ShouldNotBeNull();
        // ParamDemo1.cin 可能不包含结构体和枚举定义，所以类型定义列表可能为空
    }

    [Fact]
    public void ParseCinFile_TypeDefinitions_ShouldHaveCorrectStructure()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";

        // Act
        var result = _cinParameterService.ParseCinFile("file", null, caplPath);

        // Assert
        result.TypeDefinitions.ShouldAllBe(td => !string.IsNullOrEmpty(td.TypeName));
        result.TypeDefinitions.ShouldAllBe(td => !string.IsNullOrEmpty(td.Definition));

        // 验证类型名称不包含空格或特殊字符（基本验证）
        result.TypeDefinitions.ShouldAllBe(td => !td.TypeName.Contains(" "));
    }
}
