<template>
  <el-dialog v-model="visible" :title="title" width="80%" :before-close="handleClose" destroy-on-close
    class="param-value-dialog">
    <div class="param-value-viewer">
      <!-- 第一行：参数名称、参数类型、参数值来源 -->
      <div class="param-row">
        <div class="param-info-grid">
          <div class="param-info-item" v-if="paramName">
            <div class="param-info-label">参数名称</div>
            <div class="param-info-value param-name">{{ paramName }}</div>
          </div>

          <div class="param-info-item" v-if="paramType">
            <div class="param-info-label">数据类型</div>
            <div class="param-info-value param-type">{{ paramType }}</div>
          </div>

          <div class="param-info-item">
            <div class="param-info-label">参数值来源</div>
            <div class="param-info-value" :class="paramSource ? 'param-source' : 'no-source'">
              <el-icon v-if="paramSource" class="source-icon">
                <Document />
              </el-icon>
              {{ paramSource || 'CIN 文件' }}
            </div>
          </div>
        </div>
      </div>

      <!-- 第二行：参数说明 -->
      <div class="param-row" v-if="paramDescription">
        <div class="param-info-item">
          <div class="param-info-label">参数说明</div>
          <div class="param-info-value param-description">{{ paramDescription }}</div>
        </div>
      </div>

      <!-- 类型定义 -->
      <div class="param-row" v-if="typeDefinition && isComplexType">
        <div class="param-info-item">
          <div class="param-info-label">类型定义</div>
          <div class="param-info-value">
            <CinCodeEditor :model-value="typeDefinition" :readonly="true" height="200px" class="type-definition-editor"
              placeholder="" />
          </div>
        </div>
      </div>

      <!-- 第三行：参数值 label 和格式化滑竿 -->
      <div class="param-row">
        <div class="param-value-header">
          <div class="param-info-label">参数值</div>
          <div class="format-control" v-if="isComplexValue">
            <span class="format-label">格式化缩进</span>
            <el-slider v-model="formatLevel" :min="0" :max="6" :step="1" :show-tooltip="true" :show-stops="true"
              size="small" @input="applyFormatting" style="width: 200px;" />
          </div>
        </div>
      </div>

      <!-- 第四行：参数输入框 -->
      <div class="param-row">
        <CinCodeEditor v-model="displayValue" :readonly="readonly" :placeholder="readonly ? '' : '请输入参数值...'"
          class="param-value-editor" @change="handleCodeChange" />
      </div>
    </div>

    <template #footer v-if="!readonly">
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { Document, Delete } from '@element-plus/icons-vue';
import CinCodeEditor from './CinCodeEditor.vue';
import { formatCinValue, detectCinFormatLevel } from '@/utils/cinFormatter';

// Props
interface Props {
  modelValue: boolean;
  value: any;
  readonly?: boolean;
  title?: string;
  paramName?: string;
  paramType?: string;
  paramDescription?: string;
  paramSource?: string;
  typeDefinition?: string;
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  title: '参数值'
});

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean];
  'update:value': [value: string];
  'confirm': [value: string];
}>();

// 数据
const visible = ref(false);
const displayValue = ref('');
const formatLevel = ref(0);

// 计算属性
const isComplexValue = computed(() => {
  const value = displayValue.value.trim();
  return value.includes('{') && value.includes('}');
});

// 判断是否为复杂类型（枚举或结构体）
const isComplexType = computed(() => {
  if (!props.paramType) return false;
  const type = props.paramType.toLowerCase();
  return type.includes('struct') || type.includes('enum') || props.typeDefinition;
});

// 计算属性
const formattedValue = computed(() => {
  if (props.value === null || props.value === undefined) {
    return '';
  }

  if (typeof props.value === 'string') {
    // 直接返回原值，保持CIN格式的多行
    return props.value;
  } else {
    // 如果是对象，转换为字符串
    return String(props.value);
  }
});

// 监听器
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal;
  if (newVal) {
    displayValue.value = formattedValue.value;
  }
});

watch(visible, (newVal) => {
  emit('update:modelValue', newVal);
});

watch(() => props.value, () => {
  if (visible.value) {
    displayValue.value = formattedValue.value;
  }
});

// 方法
const handleClose = () => {
  visible.value = false;
};

const handleInput = (value: string) => {
  if (!props.readonly) {
    emit('update:value', value);
  }
};

const handleCodeChange = (value: string) => {
  if (!props.readonly) {
    emit('update:value', value);
  }
};

const handleConfirm = () => {
  if (!props.readonly) {
    emit('confirm', displayValue.value);
  }
  handleClose();
};

// 智能格式化 CIN 格式参数值
const applyFormatting = () => {
  displayValue.value = formatCinValue(displayValue.value, formatLevel.value);
};

// 监听 displayValue 变化，自动检测格式化等级
watch(displayValue, () => {
  if (isComplexValue.value) {
    formatLevel.value = detectCinFormatLevel(displayValue.value);
  }
});
</script>

<style scoped>
.param-value-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.param-value-viewer {
  display: flex;
  flex-direction: column;
  gap: 14px;
}

.param-row {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.param-value-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.format-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.format-label {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-right: 10px;
  white-space: nowrap;
}

.param-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
}

.param-info-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.param-info-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--el-text-color-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.param-info-value {
  font-size: 14px;
  color: var(--el-text-color-primary);
  word-break: break-word;
  line-height: 1.5;
}

.param-name {
  color: var(--el-text-color-primary);
}

.param-type {
  color: var(--el-text-color-primary);
}

.param-description {
  white-space: pre-wrap;
  line-height: 1.6;
}

.param-source {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--el-color-primary);
}

.source-icon {
  font-size: 14px;
}

.no-source {
  color: var(--el-text-color-placeholder);
  font-style: italic;
}

.param-value-editor {
  width: 100%;
  border-radius: 6px;
  overflow: hidden;
  font-size: 12px;
  border: 1px solid var(--el-border-color);
}

.param-value-editor :deep(.cm-editor) {
  height: 100%;
  background: var(--el-bg-color);
}

.param-value-editor :deep(.cm-focused) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

/* 类型定义编辑器样式 */
.type-definition-editor {
  font-size: 12px;
  border: 1px solid var(--el-border-color);
}

.type-definition-editor :deep(.cm-focused) {
  outline: none !important;
  box-shadow: none !important;
}

.type-definition-editor :deep(.cm-editor.cm-readonly) {
  background-color: var(--el-fill-color-lighter) !important;
}

.type-definition-editor :deep(.cm-content) {
  padding: 8px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .param-info-grid {
    grid-template-columns: 1fr;
  }

  .param-value-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .format-control {
    width: 100%;
    justify-content: flex-end;
  }
}
</style>
