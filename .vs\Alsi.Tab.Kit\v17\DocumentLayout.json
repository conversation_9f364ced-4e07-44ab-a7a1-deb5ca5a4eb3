{"Version": 1, "WorkspaceRootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{32F4AFD7-A5C8-4D09-A996-C849891054DC}|Alsi.Tab.Kit\\Alsi.Tab.Kit.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit\\cin\\templates\\can\\can_config.cin||{8B382828-6202-11D1-8870-0000F87579D2}", "RelativeMoniker": "D:0:0:{32F4AFD7-A5C8-4D09-A996-C849891054DC}|Alsi.Tab.Kit\\Alsi.Tab.Kit.csproj|solutionrelative:alsi.tab.kit\\cin\\templates\\can\\can_config.cin||{8B382828-6202-11D1-8870-0000F87579D2}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.core\\services\\sourcefileparamservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\sourcefileparamservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\alsi.tab.kit.core\\services\\capl\\caplparamconsts.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\capl\\caplparamconsts.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{32F4AFD7-A5C8-4D09-A996-C849891054DC}|Alsi.Tab.Kit\\Alsi.Tab.Kit.csproj|D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\alsi.tab.kit\\cin\\templates\\cin_templates.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}", "RelativeMoniker": "D:0:0:{32F4AFD7-A5C8-4D09-A996-C849891054DC}|Alsi.Tab.Kit\\Alsi.Tab.Kit.csproj|solutionrelative:alsi.tab.kit\\cin\\templates\\cin_templates.json||{90A6B3A7-C1A3-4009-A288-E2FF89E96FA0}"}, {"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\alsi.tab.kit.core\\services\\capl\\cin\\cintypes.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\capl\\cin\\cintypes.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "can_config.cin", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit\\cin\\templates\\can\\can_config.cin", "RelativeDocumentMoniker": "Alsi.Tab.Kit\\cin\\templates\\can\\can_config.cin", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit\\cin\\templates\\can\\can_config.cin", "RelativeToolTip": "Alsi.Tab.Kit\\cin\\templates\\can\\can_config.cin", "ViewState": "AgIAAAkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-11T00:42:33.057Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "SourceFileParamService.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\SourceFileParamService.cs", "ViewState": "AgIAAKoAAAAAAAAAAAAAAMUAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T08:41:30.199Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "CaplParamConsts.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamConsts.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamConsts.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamConsts.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\Capl\\CaplParamConsts.cs", "ViewState": "AgIAAD4AAAAAAAAAAAArwEwAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T08:41:09.249Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "cin_templates.json", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit\\cin\\templates\\cin_templates.json", "RelativeDocumentMoniker": "Alsi.Tab.Kit\\cin\\templates\\cin_templates.json", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit\\cin\\templates\\cin_templates.json", "RelativeToolTip": "Alsi.Tab.Kit\\cin\\templates\\cin_templates.json", "ViewState": "AgIAAAAAAAAAAAAAAAAAABAAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001642|", "WhenOpened": "2025-07-10T08:38:26.414Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "CinTypes.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Capl\\Cin\\CinTypes.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\Capl\\Cin\\CinTypes.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\Capl\\Cin\\CinTypes.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\Capl\\Cin\\CinTypes.cs", "ViewState": "AgIAAFUAAAAAAAAAAIAywGUAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T08:35:11.844Z"}]}]}]}