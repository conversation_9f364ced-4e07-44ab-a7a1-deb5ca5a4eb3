{"Version": 1, "WorkspaceRootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.core\\services\\cinparameterservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{EEFBA350-C69E-4E95-AFF2-F5637E80DE37}|Alsi.Tab.Kit.Core\\Alsi.Tab.Kit.Core.csproj|solutionrelative:alsi.tab.kit.core\\services\\cinparameterservice.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{D7D76682-65BB-461C-B1E9-AF038AAE0D22}|Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.web\\controllers\\cinparametercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{D7D76682-65BB-461C-B1E9-AF038AAE0D22}|Alsi.Tab.Kit.Web\\Alsi.Tab.Kit.Web.csproj|solutionrelative:alsi.tab.kit.web\\controllers\\cinparametercontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{9E8C78A1-6D4A-4D48-9D98-9829354E0C44}|Alsi.Tab.Kit.UnitTests\\Alsi.Tab.Kit.UnitTests.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.tab.kit\\alsi.tab.kit.unittests\\caplresources\\cinparser_parsecintypestests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{9E8C78A1-6D4A-4D48-9D98-9829354E0C44}|Alsi.Tab.Kit.UnitTests\\Alsi.Tab.Kit.UnitTests.csproj|solutionrelative:alsi.tab.kit.unittests\\caplresources\\cinparser_parsecintypestests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{2306ABE4-156F-48BF-A890-C67EF1C28C67}|..\\Alsi.Common\\Alsi.Common.Parsers\\Alsi.Common.Parsers.csproj|d:\\src\\005_tab\\2、src\\1、source code\\alsi.common\\alsi.common.parsers\\cin\\cinparserutils.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{6324226f-61b6-4f28-92ee-18d4b5fe1e48}"}, {"$type": "Document", "DocumentIndex": 2, "Title": "CinParser_ParseCinTypesTests.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.UnitTests\\CaplResources\\CinParser_ParseCinTypesTests.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.UnitTests\\CaplResources\\CinParser_ParseCinTypesTests.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.UnitTests\\CaplResources\\CinParser_ParseCinTypesTests.cs", "RelativeToolTip": "Alsi.Tab.Kit.UnitTests\\CaplResources\\CinParser_ParseCinTypesTests.cs", "ViewState": "AgIAAGsBAAAAAAAAAAAYwKoBAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:59:18.332Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "CinParserUtils.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParserUtils.cs", "RelativeDocumentMoniker": "..\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParserUtils.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParserUtils.cs", "RelativeToolTip": "..\\Alsi.Common\\Alsi.Common.Parsers\\Cin\\CinParserUtils.cs", "ViewState": "AgIAAE4DAAAAAAAAAAAawFsDAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:59:10.781Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "CinParameterService.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\CinParameterService.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Core\\Services\\CinParameterService.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Core\\Services\\CinParameterService.cs", "RelativeToolTip": "Alsi.Tab.Kit.Core\\Services\\CinParameterService.cs", "ViewState": "AgIAAAUAAAAAAAAAAAD4vxUAAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:50:02.492Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "CinParameterController.cs", "DocumentMoniker": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Controllers\\CinParameterController.cs", "RelativeDocumentMoniker": "Alsi.Tab.Kit.Web\\Controllers\\CinParameterController.cs", "ToolTip": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Tab.Kit\\Alsi.Tab.Kit.Web\\Controllers\\CinParameterController.cs", "RelativeToolTip": "Alsi.Tab.Kit.Web\\Controllers\\CinParameterController.cs", "ViewState": "AgIAACQAAAAAAAAAAADwvywAAAAxAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-10T05:11:51.228Z", "EditorCaption": ""}]}]}]}