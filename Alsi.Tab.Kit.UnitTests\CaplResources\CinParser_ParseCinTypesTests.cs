﻿using Alsi.Common.Parsers.Cin;
using Alsi.Common.Parsers.Cin.Models;

namespace Alsi.Tab.Kit.UnitTests.CaplResources;

public class CinParser_ParseCinTypesTests
{
    public CinParser_ParseCinTypesTests()
    {
        CinParser.Initialize();
    }

    [Fact]
    public void ParseToStructure_ShouldParseStructTypesCorrectly()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        result.ErrorMessage.ShouldBeNullOrEmpty();
        result.CodeBlocks.ShouldNotBeNull();
        result.CodeBlocks.Count.ShouldBeGreaterThan(0);

        var structDefinitionNames = result.CodeBlocks.First().ParsedStructDefinitions.Select(x => x.Name).ToArray();

        var compareStructDefinitionNames = new string[]{
            "StruSIDandSUBID",
            "StruDIDinLevel",
            "StruRoutine",
            "StruRID",
            "StruE2Esig",
            "StruE2EsigGroup",
            "StruE2EsigGroupFlexRay",
            "StruE2Emsg",
            "StruVehicleModeMsg",
            "SlotFormat",
            "StruVehicleModeMsgOnFlexRay",
            "StruE2EmsgFlexRay",
            "StruE2EdtcInfo"
        };

        for (var i = 0; i < structDefinitionNames.Length && i < compareStructDefinitionNames.Length; i++)
        {
            structDefinitionNames[i].ShouldBe(compareStructDefinitionNames[i]);
        }

        structDefinitionNames.Length.ShouldBe(compareStructDefinitionNames.Length);
    }

    [Fact]
    public void ParseToStructure_ShouldParseEnumTypesCorrectly()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";

        // Act
        var result = CinParser.ParseFile(caplPath);

        // Assert
        result.Success.ShouldBeTrue();
        result.ErrorMessage.ShouldBeNullOrEmpty();
        result.CodeBlocks.ShouldNotBeNull();
        result.CodeBlocks.Count.ShouldBeGreaterThan(0);

        var enumNames = result.CodeBlocks.First().ParsedEnumDefinitions.Select(x => x.Name).ToArray();

        var compareEnumNames = new string[]{
            "VEHICLE_CARMODE",
            "VEHICLE_USAGEMODE",
            "BUS_TYPE"
        };

        for (var i = 0; i < enumNames.Length && i < compareEnumNames.Length; i++)
        {
            enumNames[i].ShouldBe(compareEnumNames[i]);
        }

        enumNames.Length.ShouldBe(compareEnumNames.Length);
    }

    [Fact]
    public void GetTypeDefinitionInfo_ShouldReturnCorrectStructDependencies()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 测试 StruRID 结构体的依赖关系
        var typeDefInfo = result.GetTypeDefinitionInfo("StruRID");

        // Assert
        typeDefInfo.ShouldNotBeNull();
        typeDefInfo.TypeName.ShouldBe("StruRID");
        typeDefInfo.IsStructType.ShouldBeTrue();
        typeDefInfo.IsEnumType.ShouldBeFalse();
        typeDefInfo.HasCircularReference.ShouldBeFalse();

        // StruRID 依赖 StruRoutine 结构体
        var dependentStructTypes = typeDefInfo.GetAllDependentStructTypes();
        dependentStructTypes.ShouldContain("StruRoutine");

        // 验证主结构体定义
        var mainStruct = typeDefInfo.MainStructDefinition;
        mainStruct.ShouldNotBeNull();
        mainStruct.Name.ShouldBe("StruRID");

        // 验证依赖的结构体定义也被包含
        typeDefInfo.StructDefinitions.ShouldContainKey("StruRoutine");
    }

    [Fact]
    public void GetTypeDefinitionInfo_ShouldReturnCorrectNestedStructDependencies()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 测试 StruE2Emsg 结构体的依赖关系（包含嵌套结构体）
        var typeDefInfo = result.GetTypeDefinitionInfo("StruE2Emsg");

        // Assert
        typeDefInfo.ShouldNotBeNull();
        typeDefInfo.TypeName.ShouldBe("StruE2Emsg");
        typeDefInfo.IsStructType.ShouldBeTrue();

        // StruE2Emsg 依赖 StruE2EsigGroup，而 StruE2EsigGroup 又依赖 StruE2Esig
        var dependentStructTypes = typeDefInfo.GetAllDependentStructTypes();
        dependentStructTypes.ShouldContain("StruE2EsigGroup");
        dependentStructTypes.ShouldContain("StruE2Esig");

        // 验证所有依赖的结构体定义都被包含
        typeDefInfo.StructDefinitions.ShouldContainKey("StruE2EsigGroup");
        typeDefInfo.StructDefinitions.ShouldContainKey("StruE2Esig");
    }

    [Fact]
    public void GetTypeDefinitionInfo_ShouldReturnCorrectFlexRayStructDependencies()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 测试 StruVehicleModeMsgOnFlexRay 结构体的依赖关系
        var typeDefInfo = result.GetTypeDefinitionInfo("StruVehicleModeMsgOnFlexRay");

        // Assert
        typeDefInfo.ShouldNotBeNull();
        typeDefInfo.TypeName.ShouldBe("StruVehicleModeMsgOnFlexRay");
        typeDefInfo.IsStructType.ShouldBeTrue();

        // StruVehicleModeMsgOnFlexRay 依赖 SlotFormat 和 StruE2EsigGroup
        var dependentStructTypes = typeDefInfo.GetAllDependentStructTypes();
        dependentStructTypes.ShouldContain("SlotFormat");
        dependentStructTypes.ShouldContain("StruE2EsigGroup");
        dependentStructTypes.ShouldContain("StruE2Esig"); // 通过 StruE2EsigGroup 间接依赖

        // 验证所有依赖的结构体定义都被包含
        typeDefInfo.StructDefinitions.ShouldContainKey("SlotFormat");
        typeDefInfo.StructDefinitions.ShouldContainKey("StruE2EsigGroup");
        typeDefInfo.StructDefinitions.ShouldContainKey("StruE2Esig");
    }

    [Fact]
    public void GetTypeDefinitionInfo_ShouldReturnNullForNonExistentType()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 测试不存在的类型
        var typeDefInfo = result.GetTypeDefinitionInfo("NonExistentStruct");

        // Assert
        typeDefInfo.StructDefinitions.Count.ShouldBe(0);
    }

    [Fact]
    public void GetTypeDefinitionInfo_ShouldReturnCorrectEnumType()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 测试枚举类型
        var typeDefInfo = result.GetTypeDefinitionInfo("VEHICLE_CARMODE");

        // Assert
        typeDefInfo.ShouldNotBeNull();
        typeDefInfo.TypeName.ShouldBe("VEHICLE_CARMODE");
        typeDefInfo.IsEnumType.ShouldBeTrue();
        typeDefInfo.IsStructType.ShouldBeFalse();

        // 验证主枚举定义
        var mainEnum = typeDefInfo.MainEnumDefinition;
        mainEnum.ShouldNotBeNull();
        mainEnum.Name.ShouldBe("VEHICLE_CARMODE");

        // 验证枚举值
        mainEnum.Values.Count.ShouldBeGreaterThan(0);
        var normalValue = mainEnum.Values.FirstOrDefault(v => v.Name == "CARMODE_NORMAL");
        normalValue.ShouldNotBeNull();
        normalValue.HasExplicitValue.ShouldBeTrue();
        normalValue.Value.ShouldBe("0");
    }

    [Fact]
    public void GetTypeDefinitionInfo_ShouldHandleSimpleStructWithoutDependencies()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 测试简单结构体（无依赖）
        var typeDefInfo = result.GetTypeDefinitionInfo("StruE2Esig");

        // Assert
        typeDefInfo.ShouldNotBeNull();
        typeDefInfo.TypeName.ShouldBe("StruE2Esig");
        typeDefInfo.IsStructType.ShouldBeTrue();
        typeDefInfo.HasCircularReference.ShouldBeFalse();

        // StruE2Esig 是简单结构体，不依赖其他结构体
        var dependentStructTypes = typeDefInfo.GetAllDependentStructTypes();
        dependentStructTypes.Count.ShouldBe(0);

        // 但应该包含自身的定义
        typeDefInfo.StructDefinitions.ShouldContainKey("StruE2Esig");
        typeDefInfo.StructDefinitions.Count.ShouldBe(1);
    }

    [Fact]
    public void GetVariableTypeDefinition_ShouldReturnCorrectTypeInfo()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // 首先需要有一个使用结构体类型的变量，我们需要创建一个测试用例
        // 由于 GlobalElements.cin 只包含类型定义，我们测试 GetTypeDefinitionInfo 的功能

        // Act & Assert - 验证方法存在且可调用
        var typeDefInfo = result.GetVariableTypeDefinition("nonExistentVariable");
        typeDefInfo.ShouldBeNull(); // 变量不存在时应返回 null
    }

    [Fact]
    public void StructDefinition_ShouldHaveCorrectFieldInformation()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 获取 StruRID 结构体定义
        var struRid = result.StructDefinitions["StruRID"];

        // Assert
        struRid.ShouldNotBeNull();
        struRid.Name.ShouldBe("StruRID");
        struRid.Fields.Count.ShouldBe(5); // sessionMask, rid, routineType, securityLevels, struRoutineList

        // 验证字段类型
        var sessionMaskField = struRid.Fields.FirstOrDefault(f => f.Name == "sessionMask");
        sessionMaskField.ShouldNotBeNull();
        sessionMaskField.Type.ShouldBe("byte");
        sessionMaskField.IsArray.ShouldBeFalse();
        sessionMaskField.IsStructType.ShouldBeFalse();

        var ridField = struRid.Fields.FirstOrDefault(f => f.Name == "rid");
        ridField.ShouldNotBeNull();
        ridField.Type.ShouldBe("dword");

        var securityLevelsField = struRid.Fields.FirstOrDefault(f => f.Name == "securityLevels");
        securityLevelsField.ShouldNotBeNull();
        securityLevelsField.Type.ShouldBe("byte");
        securityLevelsField.IsArray.ShouldBeTrue();
        securityLevelsField.ArraySize.ShouldBe("10");

        var struRoutineListField = struRid.Fields.FirstOrDefault(f => f.Name == "struRoutineList");
        struRoutineListField.ShouldNotBeNull();
        struRoutineListField.Type.ShouldBe("struct StruRoutine");
        struRoutineListField.IsArray.ShouldBeTrue();
        struRoutineListField.ArraySize.ShouldBe("4");
        struRoutineListField.IsStructType.ShouldBeTrue();
        struRoutineListField.StructTypeName.ShouldBe("StruRoutine");
    }

    [Fact]
    public void EnumDefinition_ShouldHaveCorrectValueInformation()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 获取 VEHICLE_CARMODE 枚举定义
        var vehicleCarMode = result.EnumDefinitions["VEHICLE_CARMODE"];

        // Assert
        vehicleCarMode.ShouldNotBeNull();
        vehicleCarMode.Name.ShouldBe("VEHICLE_CARMODE");
        vehicleCarMode.Values.Count.ShouldBe(5); // NORMAL, TRANSPORT, FACTORY, CRASH, DYNO

        // 验证显式值的枚举项
        var normalValue = vehicleCarMode.Values.FirstOrDefault(v => v.Name == "CARMODE_NORMAL");
        normalValue.ShouldNotBeNull();
        normalValue.HasExplicitValue.ShouldBeTrue();
        normalValue.Value.ShouldBe("0");

        var dynoValue = vehicleCarMode.Values.FirstOrDefault(v => v.Name == "CARMODE_DYNO");
        dynoValue.ShouldNotBeNull();
        dynoValue.HasExplicitValue.ShouldBeTrue();
        dynoValue.Value.ShouldBe("5");

        // 验证隐式值的枚举项
        var transportValue = vehicleCarMode.Values.FirstOrDefault(v => v.Name == "CARMODE_TRANSPORT");
        transportValue.ShouldNotBeNull();
        transportValue.HasExplicitValue.ShouldBeFalse();
    }

    [Fact]
    public void EnumDefinition_ShouldHandleHexValues()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 获取 VEHICLE_USAGEMODE 枚举定义（包含十六进制值）
        var vehicleUsageMode = result.EnumDefinitions["VEHICLE_USAGEMODE"];

        // Assert
        vehicleUsageMode.ShouldNotBeNull();
        vehicleUsageMode.Name.ShouldBe("VEHICLE_USAGEMODE");
        vehicleUsageMode.Values.Count.ShouldBe(5); // ABANDONED, INACTIVE, CONVENIENCE, ACTIVE, DRIVING

        // 验证十六进制值
        var activeValue = vehicleUsageMode.Values.FirstOrDefault(v => v.Name == "USAGEMODE_ACTIVE");
        activeValue.ShouldNotBeNull();
        activeValue.HasExplicitValue.ShouldBeTrue();
        activeValue.Value.ShouldBe("0xB");

        var drivingValue = vehicleUsageMode.Values.FirstOrDefault(v => v.Name == "USAGEMODE_DRIVING");
        drivingValue.ShouldNotBeNull();
        drivingValue.HasExplicitValue.ShouldBeTrue();
        drivingValue.Value.ShouldBe("0xD");
    }

    [Fact]
    public void EnumDefinition_ShouldHandleSequentialValues()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 获取 BUS_TYPE 枚举定义（连续值）
        var busType = result.EnumDefinitions["BUS_TYPE"];

        // Assert
        busType.ShouldNotBeNull();
        busType.Name.ShouldBe("BUS_TYPE");
        busType.Values.Count.ShouldBe(5); // CAN_BUS, CANFD_BUS, LIN_BUS, ETH_BUS, FLEXRAY_BUS

        // 验证所有值都是隐式的（连续递增）
        foreach (var enumValue in busType.Values)
        {
            enumValue.HasExplicitValue.ShouldBeFalse();
        }

        // 验证枚举值名称
        var enumNames = busType.Values.Select(v => v.Name).ToArray();
        enumNames.ShouldContain("CAN_BUS");
        enumNames.ShouldContain("CANFD_BUS");
        enumNames.ShouldContain("LIN_BUS");
        enumNames.ShouldContain("ETH_BUS");
        enumNames.ShouldContain("FLEXRAY_BUS");
    }

    [Fact]
    public void GetTypeDefinitionInfo_ShouldHandleMaxDepthLimit()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 测试最大深度限制
        var typeDefInfo = result.GetTypeDefinitionInfo("StruE2Emsg", maxDepth: 1);

        // Assert
        typeDefInfo.ShouldNotBeNull();
        typeDefInfo.TypeName.ShouldBe("StruE2Emsg");

        // 由于深度限制为1，应该只包含直接依赖，不包含间接依赖
        // 这个测试的具体行为取决于 GetSafeStructDefinitionTree 的实现
        typeDefInfo.StructDefinitions.ShouldContainKey("StruE2Emsg");
    }

    [Fact]
    public void StructDefinition_ShouldIdentifyDependentTypes()
    {
        // Arrange
        var caplPath = "./CaplResources/GlobalElements.cin";
        var result = CinParser.ParseFile(caplPath);
        result.Success.ShouldBeTrue();

        // Act - 获取包含结构体字段的结构体定义
        var struE2EsigGroup = result.StructDefinitions["StruE2EsigGroup"];

        // Assert
        struE2EsigGroup.ShouldNotBeNull();
        struE2EsigGroup.Name.ShouldBe("StruE2EsigGroup");

        // 验证依赖的结构体类型
        struE2EsigGroup.DependentStructTypes.ShouldContain("StruE2Esig");

        // 验证结构体字段信息
        var struE2EsigListField = struE2EsigGroup.Fields.FirstOrDefault(f => f.Name == "struE2EsigList");
        struE2EsigListField.ShouldNotBeNull();
        struE2EsigListField.IsStructType.ShouldBeTrue();
        struE2EsigListField.StructTypeName.ShouldBe("StruE2Esig");
        struE2EsigListField.IsArray.ShouldBeTrue();
        struE2EsigListField.ArraySize.ShouldBe("50");
    }
}
