using Alsi.App;
using Alsi.Common.Utils;
using Alsi.Tab.Kit.Core.Models;
using Alsi.Tab.Kit.Core.Services.Capl;
using Alsi.Tab.Kit.Core.Services.Capl.Cin;
using Alsi.Tab.Kit.Core.Services.Capl.Params;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;

namespace Alsi.Tab.Kit.Core.Services
{
    public class SourceFileParamService
    {
        public async Task<List<ParsedParam>> ParseSingleFileAsync(string filePath)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var collection = new CaplParamLoader().Load(filePath);
                    return ConvertToParsedParams(collection, Path.GetFileName(filePath));
                }
                catch (Exception ex)
                {
                    throw new Exception($"解析文件 {filePath} 失败: {ex.Message}", ex);
                }
            });
        }

        /// <summary>
        /// 将 IParamCollection 转换为 ParsedParam 列表
        /// </summary>
        /// <param name="collection">参数集合</param>
        /// <param name="source">来源文件名</param>
        /// <returns>ParsedParam 列表</returns>
        private List<ParsedParam> ConvertToParsedParams(IParamCollection collection, string source)
        {
            var parsedParams = new List<ParsedParam>();

            var ecus = collection.GetEcuNodes();
            foreach (var ecu in ecus)
            {
                foreach (var regularParamValueSource in ecu.RegularParamValueSources)
                {
                    foreach (var valueSource in regularParamValueSource.GetValueSources())
                    {
                        if (!TryConvertToCin(
                            regularParamValueSource.RegularParamKey,
                            valueSource.Value,
                            out var typeName,
                            out var cinName,
                            out var cinValue))
                        {
                            AppEnv.Logger.Error(
                                $"Failed to convert param value to CIN value: Key={regularParamValueSource.RegularParamKey} Value={valueSource.Value}");
                            continue;
                        }

                        var parsedParam = new ParsedParam
                        {
                            EcuName = ecu.EcuNode,
                            Name = cinName,
                            Value = cinValue,
                            ParamType = typeName,
                            Source = source
                        };

                        parsedParams.Add(parsedParam);
                    }
                }
            }

            return parsedParams;
        }

        private bool TryConvertToCin(string regularParamKey, string paramValue, out string typeName, out string cinName, out string cinValue)
        {
            typeName = string.Empty;
            cinName = string.Empty;
            cinValue = string.Empty;

            if (paramValue == null)
            {
                return false;
            }

            // 检查是否匹配 CaplParamConsts 中的参数名称
            var caplParamAttribute = GetCaplParamAttribute(regularParamKey);
            if (caplParamAttribute == null)
            {
                AppEnv.Logger.Error($"Can't parse CAPL param attrubite of param key: {regularParamKey}");
                return false;
            }

            // CIN 的参数名，可以和标准参数名不同
            cinName = regularParamKey;
            if (!string.IsNullOrWhiteSpace(caplParamAttribute.CinName))
            {
                cinName = caplParamAttribute.CinName;
            }

            var caplParamType = caplParamAttribute.Type;
            var isHex = caplParamAttribute.IsHex;

            if (caplParamType.IsEnum)
            {
                cinValue = Enum.Parse(caplParamType, paramValue, true).ToString();
                typeName = $"enum {caplParamType.Name}";
            }
            else if (caplParamType == typeof(string))
            {
                cinValue = $"{paramValue}";
                typeName = "string";
            }
            else if (caplParamType == typeof(byte)
                || caplParamType == typeof(ushort)
                || caplParamType == typeof(short)
                || caplParamType == typeof(uint)
                || caplParamType == typeof(int)
                || caplParamType == typeof(ulong)
                || caplParamType == typeof(long))
            {
                cinValue = isHex ? $"0x{long.Parse(paramValue):X}" : $"{paramValue}";
                typeName = "number";
            }
            else if (caplParamType == typeof(byte[])
                || caplParamType == typeof(ushort[])
                || caplParamType == typeof(short[])
                || caplParamType == typeof(uint[])
                || caplParamType == typeof(int[])
                || caplParamType == typeof(ulong[])
                || caplParamType == typeof(long[]))
            {
                if (isHex)
                {
                    var items = paramValue.Trim(new[] { ']', '[' })
                        .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                        .Where(x => long.TryParse(x, out var _))
                        .Select(x => $"0x{long.Parse(x):X}")
                        .ToList();
                    cinValue = "{" + string.Join(", ", items) + "}";
                }
                else
                {
                    cinValue = $"{paramValue}";
                }

                typeName = "number[]";
            }
            else if (caplParamType == typeof(bool))
            {
                cinValue = bool.Parse(paramValue) ? "1" : "0";
                typeName = $"bool";
            }
            else
            {
                var jsonObj = JsonUtils.Deserialize(paramValue, caplParamType);
                var cinParamType = caplParamType;
                if (jsonObj is STRU_DID struDid)
                {
                    jsonObj = CinTypes.STRU_DID.FromCaplParam(struDid);
                    cinParamType = typeof(CinTypes.STRU_DID);
                }
                else if (jsonObj is IEnumerable<STRU_DID> struDids)
                {
                    jsonObj = struDids.Select(CinTypes.STRU_DID.FromCaplParam).ToArray();
                    cinParamType = typeof(CinTypes.STRU_DID[]);
                }
                else if (jsonObj is IEnumerable<STRU_DTC_EXTDATA> dtcExtDataArray)
                {
                    jsonObj = dtcExtDataArray.Select(CinTypes.STRU_DTC_EXTDATA.FromCaplParam).ToArray();
                    cinParamType = typeof(CinTypes.STRU_DTC_EXTDATA[]);
                }

                typeName = $"{jsonObj.GetType().Name}";
                var rawCinValue = CinTypeUtils.ToCinValueWithFormatting(jsonObj, cinParamType, false, true);
                // 对解析的参数值应用默认缩进格式化（层级1）
                cinValue = CinTypeUtils.FormatCinValueWithIndent(rawCinValue, 1);
            }

            return true;
        }

        private CaplParamAttribute GetCaplParamAttribute(string regularParamKey)
        {
            var caplParamConstsType = typeof(CaplParamConsts);
            var fields = caplParamConstsType.GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.DeclaredOnly);

            foreach (var field in fields)
            {
                if (field.IsLiteral && field.FieldType == typeof(string))
                {
                    var fieldValue = field.GetValue(null) as string;
                    if (fieldValue == regularParamKey)
                    {
                        return field.GetCustomAttribute<CaplParamAttribute>();
                    }
                }
            }

            return null;
        }

        public static SourceFileType GetSupportedFileType(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new AppException($"The file path is empty: {filePath}");
            }

            var extension = Path.GetExtension(filePath).ToLowerInvariant();

            switch (extension)
            {
                case ".arxml":
                    return SourceFileType.Arxml;
                case ".sddb":
                    return SourceFileType.Sddb;
                case ".ldf":
                    return SourceFileType.Ldf;
                default:
                    throw new AppException($"Can't parse file with extension: {extension}");
            }
        }
    }
}
